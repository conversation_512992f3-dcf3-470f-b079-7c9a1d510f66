// Utility to generate template data for empty or coupon designs
export function createEmptyTemplate(
  width: number,
  height: number,
  isCouponTemplate: boolean,
  backgroundColor: string = "rgb(255, 255, 255)"
): unknown[] {
  if (!isCouponTemplate) {
    return [
      {
        a: "", // name
        b: "", // notes
        c: {
          d: {
            e: { f: "RootLayer" },
            g: {
              h: { i: width, j: height }, // boxSize
              k: { l: 0, m: 0 }, // position
              n: 0, // rotate
              o: backgroundColor, // color
              p: null, // image
              q: null, // additional props
            },
            r: false, // locked
            t: null, // parent
            s: [], // children
          },
        },
      },
    ];
  }

  // Coupon template structure
  return [
    {
      a: "",
      b: "",
      c: {
        d: {
          e: { f: "RootLayer" },
          g: {
            h: { i: 1200, j: 350 },
            k: { l: 0, m: 0 },
            n: 0,
            o: backgroundColor,
            p: null,
            q: null,
          },
          r: false,
          s: [
            "ca_33sfQjR",
            "ca_zeXoT7W",
            "ca_mQt8HuL",
            "ca_lVSszfR",
            "ca_5N1tfNw",
            "ca_64Y9RPs",
            "ca_nwE3TC5",
            "ca_JMrAkmI",
            "ca_V3geT2j",
            "ca_dRv5jxR",
            "ca_IanmMZh",
            "ca_x0YAo5Z",
            "ca_AsfKuQB",
            "ca_lW3G0ES",
            "ca_WOt1HWA",
            "ca_ZxGc8dq",
            "ca_UtbgySe",
            "ca_SqUBwDf",
            "ca_4CKWq20",
            "ca_YYziAvZ",
          ],
          t: null,
        },
      },
    },
  ];
}

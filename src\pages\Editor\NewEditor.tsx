/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { useState, useEffect, useCallback } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { toast } from "sonner";
import axios from "axios";
import "@/styles/editor.css";
import { Button } from "@/components/ui/button";

import { templateService } from "@/services/templateService";
import { liveMenuService } from "@/services/liveMenuService";
import { data } from "@/sampleData";
import { createEmptyTemplate, createCouponTemplate } from "./templates";
import {
  GET_TEMPLATE_ENDPOINT,
  GET_TEMPLATE_PATH_ENDPOINT,
  USER_KIOSK_ENDPOINT,
  USER_LIVEMENU_ENDPOINT,
} from "canva-editor/utils/constants/api";
import CustomCanvaEditor from "@/components/editor/CustomCanvaEditor";
import CouponCampaignDialog from "@/components/editor/CouponCampaignDialog";
import CouponVerifyDialog from "@/components/editor/CouponVerifyDialog";
import PublishKioskDialog from "@/components/editor/PublishKioskDialog";
import PublishLiveMenuDialog from "@/components/editor/PublishLiveMenuDialog";
import { useAuth } from "@/contexts/AuthContext";
import Cookies from "js-cookie";

interface NewEditorProps {
  isAdmin?: boolean;
}

const NewEditor: React.FC<NewEditorProps> = ({ isAdmin = false }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();

  // State management
  const [loading, setLoading] = useState(false);
  const [templateData, setTemplateData] = useState<any>(null);
  const [name, setName] = useState<string>("Untitled Design");
  const [isKiosk, setIsKiosk] = useState(false);
  const [isLiveMenu, setIsLiveMenu] = useState(false);
  const [isCoupon, setIsCoupon] = useState(false);
  const [isInCouponTemplateMode, setIsInCouponTemplateMode] = useState(false);
  const [couponCampaignData, setCouponCampaignData] = useState<any>(null);
  const [showCouponCampaignDialog, setShowCouponCampaignDialog] = useState(false);
  const [showCouponVerifyDialog, setShowCouponVerifyDialog] = useState(false);
  const [showKioskDialog, setShowKioskDialog] = useState(false);
  const [showLiveMenuDialog, setShowLiveMenuDialog] = useState(false);

  // Function to create templates with custom dimensions
  const createTemplate = useCallback(
    (
      width: number,
      height: number,
      isC: boolean,
      backgroundColor: string = "rgb(255, 255, 255)"
    ) => {
      try {
        console.log("createTemplate called with isC:", isC);
        
        if (isC) {
          console.log("Setting coupon template");
          const template = createCouponTemplate();
          setTemplateData(template);
          setName("Untitled Coupon");
        } else {
          console.log("Setting empty template");
          const template = createEmptyTemplate(width, height, backgroundColor);
          setTemplateData(template);
          setName("Untitled Design");
        }
        // toast.success("Custom size design created");
      } catch (error) {
        console.error("Error creating template:", error);
        toast.error("Failed to create custom size design");
        // Fallback to sample data
        setTemplateData(data);
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // Function to load kiosk data
  const loadKioskData = useCallback(
    async (_kioskId: string) => {
      try {
        setLoading(true);

        // Get user ID from auth context or cookies
        const userId = user?.userId || Cookies.get("auth_token") || "";
        console.log(userId);
        // Fetch kiosk data
        const response = await axios.get(USER_KIOSK_ENDPOINT, {
          params: { userId },
        });
        console.log(response);
        const kiosk = response.data.kiosk;
        console.log(kiosk);
        if (kiosk && kiosk.templateUrl) {
          // Load the kiosk template data from templateUrl using proxy (same as KioskContent.tsx)
          try {
            console.log(
              "Fetching template through proxy with complete URL:",
              kiosk.templateUrl
            );
            const templateResponse = await axios.get(
              `/api/proxy-template/${encodeURIComponent(kiosk.templateUrl)}`
            );
            console.log("Template response:", templateResponse.data);
            setTemplateData(templateResponse.data);
            setName(kiosk.name || "Kiosk Design");
          } catch (templateError) {
            console.error("Error fetching template:", templateError);
            toast.error("Failed to load kiosk template");
            setTemplateData(data);
          }
        } else {
          console.log("No kiosk found or no templateUrl, using default data");
          setTemplateData(data);
        }
      } catch (error) {
        console.error("Error loading kiosk data:", error);
        toast.error("Failed to load kiosk data");
        setTemplateData(data);
      } finally {
        setLoading(false);
      }
    },
    [user?.userId]
  );

  // Function to load live menu data
  const loadLiveMenuData = useCallback(
    async (_liveMenuId: string) => {
      try {
        setLoading(true);

        // Get user ID from auth context or cookies
        const userId = user?.userId || Cookies.get("auth_token") || "";
        console.log(userId);
        // Fetch live menu data
        const response = await axios.get(USER_LIVEMENU_ENDPOINT, {
          params: { userId },
        });
        console.log(response);
        const liveMenu = response.data.liveMenu;
        console.log(liveMenu);
        if (liveMenu && liveMenu.templateUrl) {
          // Load the live menu template data from templateUrl using proxy
          try {
            console.log(
              "Fetching template through proxy with complete URL:",
              liveMenu.templateUrl
            );
            const templateResponse = await axios.get(
              `/api/proxy-template/${encodeURIComponent(liveMenu.templateUrl)}`
            );
            console.log("Template response:", templateResponse.data);
            setTemplateData(templateResponse.data);
            setName(liveMenu.name || "Live Menu Design");
          } catch (templateError) {
            console.error("Error fetching template:", templateError);
            toast.error("Failed to load live menu template");
            setTemplateData(data);
          }
        } else {
          console.log("No live menu found or no templateUrl, using default data");
          setTemplateData(data);
        }
      } catch (error) {
        console.error("Error loading live menu data:", error);
        toast.error("Failed to load live menu data");
        setTemplateData(data);
      } finally {
        setLoading(false);
      }
    },
    [user?.userId]
  );

  // Function to load template data
  const loadTemplateData = useCallback(
    async (templateId: string) => {
      try {
        setLoading(true);
        console.log("Loading template with ID:", templateId);

        // First try to get the template path
        const pathResponse = await axios.get(GET_TEMPLATE_PATH_ENDPOINT, {
          params: { templateId },
        });

        if (pathResponse.data && pathResponse.data.templateUrl) {
          console.log("Template URL found:", pathResponse.data.templateUrl);
          // Use proxy to fetch the template
          const templateResponse = await axios.get(
            `/api/proxy-template/${encodeURIComponent(pathResponse.data.templateUrl)}`
          );
          console.log("Template data loaded:", templateResponse.data);
          setTemplateData(templateResponse.data);
          setName(pathResponse.data.name || "Untitled Design");
        } else {
          console.log("No template URL found, using fallback");
          setTemplateData(data);
        }
      } catch (error) {
        console.error("Error loading template:", error);
        toast.error("Failed to load template");
        setTemplateData(data);
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // Handle coupon campaign dialog success
  const handleCouponCampaignSuccess = (result: string | { type: 'template-edit-mode'; data: any }) => {
    if (typeof result === 'object' && result.type === 'template-edit-mode') {
      console.log("Entering template edit mode for coupon campaign");
      setIsInCouponTemplateMode(true);
      setCouponCampaignData(result.data);
      
      // Use campaign name as project name
      if (result.data.campaignName) {
        setName(result.data.campaignName);
        console.log("Set project name to campaign name:", result.data.campaignName);
      }
      
      setShowCouponCampaignDialog(false);
    } else {
      console.log("Coupon campaign created:", result);
      setShowCouponCampaignDialog(false);
      if (typeof result === 'string') {
        localStorage.setItem("currentCouponCampaignId", result);
      }
    }
  };

  // Handle bulk generate from editor
  const handleBulkGenerateFromEditor = () => {
    console.log("Bulk generate triggered from editor");
    if (isInCouponTemplateMode && couponCampaignData) {
      console.log("Campaign data available:", couponCampaignData);
      setShowCouponVerifyDialog(true);
    } else {
      toast.error("No coupon campaign data available");
    }
  };

  // Initialize based on URL parameters
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const templateId = params.get("templateId");
    const kioskId = params.get("kioskId");
    const liveMenuId = params.get("liveMenuId");
    const width = params.get("width");
    const height = params.get("height");
    const backgroundColor = params.get("backgroundColor");
    const isC = params.get("isC") === "true";

    if (templateId) {
      loadTemplateData(templateId);
    } else if (kioskId) {
      setIsKiosk(true);
      loadKioskData(kioskId);
    } else if (liveMenuId) {
      setIsLiveMenu(true);
      loadLiveMenuData(liveMenuId);
    } else if (width && height) {
      const w = parseInt(width);
      const h = parseInt(height);
      if (!isNaN(w) && !isNaN(h)) {
        if (isC) {
          setIsCoupon(true);
          setShowCouponCampaignDialog(true);
        }
        createTemplate(w, h, isC, backgroundColor || "rgb(255, 255, 255)");
      } else {
        setTemplateData(data);
      }
    } else {
      setTemplateData(data);
    }
  }, [location.search, loadTemplateData, loadKioskData, loadLiveMenuData, createTemplate]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  if (!templateData) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-lg">No template data available</div>
      </div>
    );
  }

  return (
    <div className="h-screen w-full overflow-hidden">
      <CustomCanvaEditor
        data={{
          name,
          editorConfig: templateData,
        }}
        isAdmin={isAdmin}
        isKiosk={isKiosk}
        isLiveMenu={isLiveMenu}
        isCoupon={isCoupon}
        isInCouponTemplateMode={isInCouponTemplateMode}
        onBulkGenerate={handleBulkGenerateFromEditor}
        onShare={() => {
          if (isKiosk) {
            setShowKioskDialog(true);
          } else if (isLiveMenu) {
            setShowLiveMenuDialog(true);
          } else {
            console.log("Share functionality not implemented for this type");
          }
        }}
        onDownload={() => {
          console.log("Download functionality");
        }}
      />

      {/* Coupon Campaign Dialog */}
      {showCouponCampaignDialog && (
        <CouponCampaignDialog
          onClose={() => setShowCouponCampaignDialog(false)}
          onSuccess={handleCouponCampaignSuccess}
        />
      )}

      {/* Coupon Verify Dialog */}
      {showCouponVerifyDialog && couponCampaignData && (
        <CouponVerifyDialog
          isOpen={showCouponVerifyDialog}
          onClose={() => setShowCouponVerifyDialog(false)}
          campaignData={couponCampaignData}
          templateImageUrl={undefined}
        />
      )}

      {/* Kiosk Dialog */}
      {showKioskDialog && (
        <PublishKioskDialog
          isOpen={showKioskDialog}
          onClose={() => setShowKioskDialog(false)}
          templateImageUrl={undefined}
        />
      )}

      {/* Live Menu Dialog */}
      {showLiveMenuDialog && (
        <PublishLiveMenuDialog
          isOpen={showLiveMenuDialog}
          onClose={() => setShowLiveMenuDialog(false)}
          templateImageUrl={undefined}
        />
      )}

      {/* Note: CSS for horizontal scrollbars is in styles/editor.css */}
    </div>
  );
};

export default NewEditor;

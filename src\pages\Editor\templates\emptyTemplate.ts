// Empty template for creating new designs
export const createEmptyTemplate = (
  width: number,
  height: number,
  backgroundColor: string = "rgb(255, 255, 255)"
) => {
  return [
    {
      a: "", // name
      b: "", // notes
      c: {
        // layers
        d: {
          // ROOT layer
          e: {
            f: "RootLayer", // type.resolvedName
          },
          g: {
            h: {
              i: width, // boxSize.width
              j: height, // boxSize.height
            },
            k: {
              l: 0, // position.x
              m: 0, // position.y
            },
            n: 0, // rotate
            o: backgroundColor, // color
            p: null, // image
            q: null, // additional props
          },
          r: false, // locked
          t: null, // parent
          s: [], // child
        },
      },
    },
  ];
};

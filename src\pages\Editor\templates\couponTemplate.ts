// Coupon template for creating coupon designs
export const couponTemplate = () => [
  {
    a: "",
    b: "",
    c: {
      d: {
        e: { f: "RootLayer" },
        g: {
          h: { i: 1200, j: 350 },
          k: { l: 0, m: 0 },
          n: 0,
          o: "rgb(253, 205, 7)",
          p: null,
          q: null,
        },
        r: false,
        s: [
          "ca_33sfQjR",
          "ca_zeXoT7W",
          "ca_mQt8HuL",
          "ca_lVSszfR",
          "ca_5N1tfNw",
          "ca_64Y9RPs",
          "ca_nwE3TC5",
          "ca_JMrAkmI",
          "ca_V3geT2j",
          "ca_dRv5jxR",
          "ca_IanmMZh",
          "ca_x0YAo5Z",
          "ca_AsfKuQB",
          "ca_lW3G0ES",
          "ca_WOt1HWA",
          "ca_ZxGc8dq",
          "ca_UtbgySe",
          "ca_SqUBwDf",
          "ca_4CKWq20",
        ],
        t: null,
      },
      ca_33sfQjR: {
        e: { f: "ShapeLayer" },
        g: {
          k: { l: 909.8016042976262, m: 114.44730879945041 },
          h: {
            i: 260.1592064120906,
            j: 177.8778090723456,
            l: 909.8016042976262,
            m: 114.44730879945041,
          },
          n: 0,
          ah: "M 40.3175 0 L 215.683 0 C 237.949 0 256 18.0507 256 40.3175 L 256 215.683 C 256 237.949 237.***********.683 256 L 40.3175 256 C 18.0507 256 0 237.949 0 215.683 L 0 40.3175 C 0 18.0507 18.0507 0 40.3175 0 Z",
          u: 0.9224087405965519,
          o: "rgb(255, 255, 255)",
          ai: { i: 256, j: 256 },
          ag: 1,
          q: null,
        },
        r: true,
        ap: true,
        s: [],
        t: "ROOT",
      },
      ca_zeXoT7W: {
        e: { f: "ShapeLayer" },
        g: {
          k: { l: 175, m: 392.5 },
          h: { i: 105, j: 105 },
          n: 0,
          ah: "M 0 0 L 256 0 L 256 256 L 0 256 Z",
          u: 0.6,
          o: "rgb(123, 68, 68)",
          ai: { i: 256, j: 256 },
        },
        r: false,
        s: [],
        t: "ROOT",
      },
      ca_mQt8HuL: {
        e: { f: "ShapeLayer" },
        g: {
          k: { l: 908.0436468780458, m: 174 },
          h: {
            i: 263.6751212512511,
            j: 20,
            l: 907.0829768768833,
            m: 174,
          },
          n: 0,
          ah: "M 0 0 L 256 0 L 256 256 L 0 256 Z",
          u: 0.6,
          o: "rgb(253, 205, 7)",
          ai: { i: 256, j: 256 },
          ag: 1,
          q: null,
        },
        r: true,
        ap: true,
        s: [],
        t: "ROOT",
      },
      ca_lVSszfR: {
        e: { f: "TextLayer" },
        g: {
          k: { l: 891.0995947745712, m: 19.55440637906683 },
          h: {
            i: 145.97892685318828,
            j: 22.991680979377158,
            l: 891.0995947745712,
            m: 16.098710501664726,
          },
          u: 0.36494731713297074,
          n: 0,
          v: '<p style="text-align: center;font-family: \'Canva Sans Regular\';font-size: 45px;color: rgb(0, 0, 0);line-height: 1.4;letter-spacing: normal;"><strong><span style="color: rgb(0, 0, 0);">Powered By</span></strong></p>',
          w: [
            {
              x: "Canva Sans",
              a: "Canva Sans Regular",
              y: "http://fonts.gstatic.com/s/alexandria/v3/UMBCrPdDqW66y0Y2usFeQCH18mulUxBvI9r7TqbCHJ8BRq0b.woff2",
              z: "regular",
              aa: [],
            },
          ],
          ab: ["rgb(0, 0, 0)"],
          ac: [45],
          ad: null,
          ag: 1,
        },
        r: true,
        ap: true,
        s: [],
        t: "ROOT",
      },
      ca_5N1tfNw: {
        e: { f: "ShapeLayer" },
        g: {
          k: { l: 256, m: 501.5 },
          h: { i: 105, j: 105 },
          n: 0,
          ah: "M 40.3175 0 L 215.683 0 C 237.949 0 256 18.0507 256 40.3175 L 256 215.683 C 256 237.949 237.***********.683 256 L 40.3175 256 C 18.0507 256 0 237.949 0 215.683 L 0 40.3175 C 0 18.0507 18.0507 0 40.3175 0 Z",
          u: 0.6,
          o: "rgb(123, 68, 68)",
          ai: { i: 256, j: 256 },
        },
        r: false,
        s: [],
        t: "ROOT",
      },
      ca_64Y9RPs: {
        e: { f: "ShapeLayer" },
        g: {
          k: { l: 915.4746634737357, m: 51.39453737478598 },
          h: {
            i: 195.49701638889462,
            j: 45.370182999565536,
            l: 915.1766465676396,
            m: 50.69301011595734,
          },
          n: 0,
          ah: "M 40.3175 0 L 215.683 0 C 237.949 0 256 18.0507 256 40.3175 L 256 215.683 C 256 237.949 237.***********.683 256 L 40.3175 256 C 18.0507 256 0 237.949 0 215.683 L 0 40.3175 C 0 18.0507 18.0507 0 40.3175 0 Z",
          u: 0.6,
          o: "rgb(59, 59, 59)",
          ai: { i: 256, j: 256 },
          ag: 1,
          q: null,
        },
        r: true,
        ap: true,
        s: [],
        t: "ROOT",
      },
      ca_nwE3TC5: {
        e: { f: "TextLayer" },
        g: {
          k: { l: 897.2991785875431, m: 55.82159996436795 },
          h: {
            i: 231.84798616128006,
            j: 36.51605782040161,
            l: 939.8707819680167,
            m: 39.079628874568755,
          },
          u: 0.5796199654032002,
          n: 0,
          v: '<p style="text-align: center;font-family: \'Canva Sans Regular\';font-size: 45px;color: rgb(255, 255, 255);line-height: 1.4;letter-spacing: normal;"><strong><span style="color: rgb(255, 255, 255);">FoodyQueen</span></strong></p>',
          w: [
            {
              x: "Canva Sans",
              a: "Canva Sans Regular",
              y: "http://fonts.gstatic.com/s/alexandria/v3/UMBCrPdDqW66y0Y2usFeQCH18mulUxBvI9r7TqbCHJ8BRq0b.woff2",
              z: "regular",
              aa: [],
            },
          ],
          ab: ["rgb(255, 255, 255)"],
          ac: [45],
          ad: null,
          ag: 1,
        },
        r: true,
        ap: true,
        s: [],
        t: "ROOT",
      },
      ca_JMrAkmI: {
        e: { f: "TextLayer" },
        g: {
          k: { l: 887.7567243669338, m: 120.98570765076599 },
          h: {
            i: 304.2489662734755,
            j: 47.919212188072386,
            l: 892.3168364421191,
            m: 114.44730879945041,
          },
          u: 0.7606224156836887,
          n: 0,
          v: '<p style="text-align: center;font-family: \'Canva Sans Regular\';font-size: 45px;color: rgb(0, 0, 0);line-height: 1.4;letter-spacing: normal;"><strong><span style="color: rgb(0, 0, 0);">CODE</span></strong></p>',
          w: [
            {
              x: "Canva Sans",
              a: "Canva Sans Regular",
              y: "http://fonts.gstatic.com/s/alexandria/v3/UMBCrPdDqW66y0Y2usFeQCH18mulUxBvI9r7TqbCHJ8BRq0b.woff2",
              z: "regular",
              aa: [],
            },
          ],
          ab: ["rgb(0, 0, 0)"],
          ac: [45],
          ad: null,
          ag: 1,
        },
        r: true,
        ap: true,
        s: [],
        t: "ROOT",
      },
      ca_V3geT2j: {
        e: { f: "TextLayer" },
        g: {
          k: { l: 909.8016042976261, m: 210.78743693455175 },
          h: {
            i: 263.4009478524649,
            j: 56.8185577187362,
            l: 928.6047427879444,
            m: 196.64239786131517,
          },
          u: 2.582661714488009,
          n: 0,
          v: '<p style="text-align: center;font-family: \'Canva Sans Regular\';font-size: 16px;color: rgb(0, 0, 0);line-height: 1.4;letter-spacing: normal;"><strong><span style="color: rgb(0, 0, 0);">{auto-code}</span></strong></p>',
          w: [
            {
              x: "Canva Sans",
              a: "Canva Sans Regular",
              y: "http://fonts.gstatic.com/s/alexandria/v3/UMBCrPdDqW66y0Y2usFeQCH18mulUxBvI9r7TqbCHJ8BRq0b.woff2",
              z: "regular",
              aa: [],
            },
          ],
          ab: ["rgb(0, 0, 0)"],
          ac: [16],
          ad: null,
          ag: 1,
        },
        r: true,
        ap: true,
        s: [],
        t: "ROOT",
      },
      ca_dRv5jxR: {
        e: { f: "ShapeLayer" },
        g: {
          k: { l: 155, m: 357.5 },
          h: { i: 105, j: 105 },
          n: 0,
          ah: "M 0 0 L 256 0 L 256 256 L 0 256 Z",
          u: 0.6,
          o: "rgb(123, 68, 68)",
          ai: { i: 256, j: 256 },
        },
        r: false,
        s: [],
        t: "ROOT",
      },
      ca_IanmMZh: {
        e: { f: "GroupLayer" },
        g: {
          k: { l: 860.8315134560204, m: -9.142747505755949 },
          h: { i: 20, j: 370.81776262744665 },
          u: 1,
          n: 0,
        },
        r: true,
        ap: true,
        s: [
          "ca_h7W5cb4",
          "ca_phgXNfs",
          "ca_dSbk48x",
          "ca_ukjkZCD",
          "ca_auQ3uoK",
          "ca_A8Ru9Rh",
          "ca_0okC1Bv",
          "ca_RCIAjtq",
          "ca_5M31iMh",
          "ca_L9PYs8o",
          "ca_uCIZXX3",
          "ca_TLLvqJ7",
          "ca_CDwJcJG",
          "ca_i1MEc8j",
        ],
        t: "ROOT",
      },
